@extends('layouts.app')

@section('title', 'แพ็กเกจ - ผู้ใหญ่จากบริการ')

@section('content')
<!-- Hero Section -->
<section class="hero-section text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    <i class="fas fa-box me-3"></i>แพ็กเกจพิเศษ
                </h1>
                <p class="lead mb-4">เลือกแพ็กเกจที่เหมาะกับคุณ ราคาเป็นมิตร คุณภาพสูง</p>
                <a href="#packages" class="btn btn-light btn-lg px-4">
                    <i class="fas fa-arrow-down me-2"></i>ดูแพ็กเกจทั้งหมด
                </a>
            </div>
            <div class="col-lg-6 text-center">
                <i class="fas fa-boxes fa-10x opacity-75"></i>
            </div>
        </div>
    </div>
</section>

<!-- Packages Section -->
<section id="packages" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">แพ็กเกจของเรา</h2>
            <p class="text-muted lead">เลือกแพ็กเกจที่เหมาะกับความต้องการของคุณ</p>
        </div>

        @if($packages->count() > 0)
            <div class="row g-4">
                @foreach($packages as $package)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($package->image) }}"
                             class="card-img-top"
                             alt="{{ $package->name }}"
                             style="height: 200px; object-fit: cover;">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title fw-bold text-primary">{{ $package->name }}</h5>
                            <p class="card-text text-muted flex-grow-1">{{ Str::limit($package->description, 120) }}</p>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <span class="h4 text-success fw-bold mb-0">
                                    <i class="fas fa-tag me-1"></i>฿{{ number_format($package->price) }}
                                </span>
                                <a href="{{ route('packages.show', $package) }}" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">ยังไม่มีแพ็กเกจ</h3>
                <p class="text-muted">เรากำลังเตรียมแพ็กเกจพิเศษสำหรับคุณ</p>
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3 class="fw-bold mb-3">ต้องการแพ็กเกจพิเศษ?</h3>
                <p class="lead mb-0">ติดต่อเราเพื่อขอแพ็กเกจที่เหมาะกับความต้องการของคุณ</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/contact" class="btn btn-primary btn-lg">
                    <i class="fas fa-phone me-2"></i>ติดต่อเรา
                </a>
            </div>
        </div>
    </div>
</section>
@endsection 