<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomepageContentController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\ContactController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// ===================== หน้าบ้าน (Public) =====================
Route::get('/', function () {
    $contents = \App\Models\HomepageContent::all();
    return view('home', compact('contents'));
})->name('home');
Route::get('/services', [ServiceController::class, 'index'])->name('services.index');
Route::get('/services/{service}', [ServiceController::class, 'show'])->name('services.show');
Route::get('/packages', [PackageController::class, 'index'])->name('packages.index');
Route::get('/packages/{package}', [PackageController::class, 'show'])->name('packages.show');
Route::get('/articles', [ArticleController::class, 'index'])->name('articles.index');
Route::get('/articles/{article}', [ArticleController::class, 'show'])->name('articles.show');
Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// ===================== หลังบ้าน (Admin) =====================
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', function() { return view('admin.dashboard'); })->name('dashboard');

    // Routes with file upload checking
    Route::middleware(['check.file.upload'])->group(function () {
        Route::post('services', [ServiceController::class, 'store'])->name('services.store');
        Route::put('services/{service}', [ServiceController::class, 'update'])->name('services.update');
        Route::patch('services/{service}', [ServiceController::class, 'update']);

        Route::post('packages', [PackageController::class, 'store'])->name('packages.store');
        Route::put('packages/{package}', [PackageController::class, 'update'])->name('packages.update');
        Route::patch('packages/{package}', [PackageController::class, 'update']);

        Route::post('articles', [ArticleController::class, 'store'])->name('articles.store');
        Route::put('articles/{article}', [ArticleController::class, 'update'])->name('articles.update');
        Route::patch('articles/{article}', [ArticleController::class, 'update']);

        Route::put('homepage/{homepage}', [HomepageContentController::class, 'update'])->name('homepage.update');
        Route::patch('homepage/{homepage}', [HomepageContentController::class, 'update']);
    });

    // Routes without file upload
    Route::resource('services', ServiceController::class)->except(['show', 'store', 'update']);
    Route::resource('packages', PackageController::class)->except(['show', 'store', 'update']);
    Route::resource('articles', ArticleController::class)->except(['show', 'store', 'update']);
    Route::resource('homepage', HomepageContentController::class)->only(['index','edit'])->except(['update']);
    Route::resource('contacts', ContactController::class)->only(['index','edit','update','destroy']);

    // Site Settings Routes
    Route::get('settings', [App\Http\Controllers\SiteSettingController::class, 'index'])->name('settings.index');
    Route::put('settings', [App\Http\Controllers\SiteSettingController::class, 'update'])->name('settings.update');
    Route::delete('settings/remove-logo', [App\Http\Controllers\SiteSettingController::class, 'removeLogo'])->name('settings.remove-logo');
    Route::delete('settings/remove-favicon', [App\Http\Controllers\SiteSettingController::class, 'removeFavicon'])->name('settings.remove-favicon');
});

// Authentication Routes
Auth::routes();

// Home route - redirect to admin for authenticated users
Route::get('/home', function () {
    if (auth()->check() && auth()->user()->is_admin) {
        return redirect()->route('admin.dashboard');
    }
    return redirect('/');
})->middleware('auth')->name('home.index');
