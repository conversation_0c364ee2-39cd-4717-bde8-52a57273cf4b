
<?php $__env->startSection('content'); ?>
    <h1>แก้ไขเนื้อหาหน้าแรก</h1>
    <form action="<?php echo e(route('admin.homepage.update', $homepage_content)); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?> <?php echo method_field('PUT'); ?>
        <div class="mb-3">
            <label>Section</label>
            <input type="text" name="section" class="form-control" value="<?php echo e($homepage_content->section); ?>">
        </div>
        <div class="mb-3">
            <label>หัวข้อ</label>
            <input type="text" name="title" class="form-control" value="<?php echo e($homepage_content->title); ?>">
        </div>
        <div class="mb-3">
            <label>เนื้อหา</label>
            <textarea name="content" class="form-control"><?php echo e($homepage_content->content); ?></textarea>
        </div>
        <div class="mb-3">
            <label>รูปภาพ</label>
            <?php if($homepage_content->image): ?>
                <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($homepage_content->image)); ?>"
                     style="max-width:120px;display:block;"
                     alt="รูปภาพหน้าแรก"
                     class="img-thumbnail mb-2">
            <?php endif; ?>
            <input type="file" name="image" class="form-control">
        </div>
        <div class="mb-3">
            <label>ข้อความปุ่ม</label>
            <input type="text" name="button_text" class="form-control" value="<?php echo e($homepage_content->button_text); ?>">
        </div>
        <div class="mb-3">
            <label>ลิงก์ปุ่ม</label>
            <input type="text" name="button_link" class="form-control" value="<?php echo e($homepage_content->button_link); ?>">
        </div>
        <button type="submit" class="btn btn-success">บันทึก</button>
        <a href="<?php echo e(route('admin.homepage.index')); ?>" class="btn btn-secondary">ย้อนกลับ</a>
    </form>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/homepage/edit.blade.php ENDPATH**/ ?>