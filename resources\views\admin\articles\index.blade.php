@extends('layouts.app')

@section('title', 'จัดการบทความ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-newspaper me-2"></i>จัดการบทความ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item active">จัดการบทความ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการบทความทั้งหมด</h3>
                            <div class="card-tools">
                                <div class="input-group input-group-sm" style="width: 200px;">
                                    <input type="text" name="search" class="form-control float-right" placeholder="ค้นหาบทความ..." id="searchInput">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-default">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                                <a href="{{ route('admin.articles.create') }}" class="btn btn-primary ml-2">
                                    <i class="fas fa-plus me-1"></i>เพิ่มบทความใหม่
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if($articles->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 80px;">รูปภาพ</th>
                                                <th>หัวข้อ</th>
                                                <th>เนื้อหา</th>
                                                <th style="width: 120px;">วันที่เผยแพร่</th>
                                                <th style="width: 150px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($articles as $article)
                                                <tr>
                                                    <td class="text-center">
                                                        <img src="{{ \App\Helpers\ImageHelper::getImageUrl($article->image) }}"
                                                             class="img-thumbnail"
                                                             style="max-width: 60px; max-height: 60px;"
                                                             alt="รูปภาพบทความ">
                                                    </td>
                                                    <td>
                                                        <strong>{{ $article->title }}</strong>
                                                    </td>
                                                    <td>
                                                        {{ Str::limit($article->content, 80) }}
                                                    </td>
                                                    <td class="text-center">
                                                        @if($article->published_at)
                                                            <span class="badge badge-success">
                                                                {{ $article->published_at->format('d/m/Y') }}
                                                            </span>
                                                        @else
                                                            <span class="badge badge-secondary">ไม่ระบุ</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.articles.edit', $article) }}"
                                                               class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <form action="{{ route('admin.articles.destroy', $article) }}"
                                                                  method="POST"
                                                                  style="display:inline;"
                                                                  onsubmit="return confirm('ยืนยันการลบบทความ {{ $article->title }}?')">
                                                                @csrf @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-danger">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีบทความ</h5>
                                    <p class="text-muted">เริ่มต้นโดยการเพิ่มบทความแรกของคุณ</p>
                                    <a href="{{ route('admin.articles.create') }}" class="btn btn-primary">
                                        <i class="fas fa-plus me-1"></i>เพิ่มบทความใหม่
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Search functionality
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('tbody tr');

    tableRows.forEach(row => {
        const title = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const content = row.querySelector('td:nth-child(3)').textContent.toLowerCase();

        if (title.includes(searchTerm) || content.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});
</script>
@endsection