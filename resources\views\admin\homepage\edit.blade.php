@extends('layouts.app')
@section('content')
    <h1>แก้ไขเนื้อหาหน้าแรก</h1>
    <form action="{{ route('admin.homepage.update', $homepage_content) }}" method="POST" enctype="multipart/form-data">
        @csrf @method('PUT')
        <div class="mb-3">
            <label>Section</label>
            <input type="text" name="section" class="form-control" value="{{ $homepage_content->section }}">
        </div>
        <div class="mb-3">
            <label>หัวข้อ</label>
            <input type="text" name="title" class="form-control" value="{{ $homepage_content->title }}">
        </div>
        <div class="mb-3">
            <label>เนื้อหา</label>
            <textarea name="content" class="form-control">{{ $homepage_content->content }}</textarea>
        </div>
        <div class="mb-3">
            <label>รูปภาพ</label>
            @if($homepage_content->image)
                <img src="{{ \App\Helpers\ImageHelper::getImageUrl($homepage_content->image) }}"
                     style="max-width:120px;display:block;"
                     alt="รูปภาพหน้าแรก"
                     class="img-thumbnail mb-2">
            @endif
            <input type="file" name="image" class="form-control">
        </div>
        <div class="mb-3">
            <label>ข้อความปุ่ม</label>
            <input type="text" name="button_text" class="form-control" value="{{ $homepage_content->button_text }}">
        </div>
        <div class="mb-3">
            <label>ลิงก์ปุ่ม</label>
            <input type="text" name="button_link" class="form-control" value="{{ $homepage_content->button_link }}">
        </div>
        <button type="submit" class="btn btn-success">บันทึก</button>
        <a href="{{ route('admin.homepage.index') }}" class="btn btn-secondary">ย้อนกลับ</a>
    </form>
@endsection 