@extends('layouts.app')

@section('title', 'แก้ไขบทความ - Admin Panel')

@section('content')
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-edit me-2"></i>แก้ไขบทความ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.articles.index') }}">จัดการบทความ</a></li>
                        <li class="breadcrumb-item active">แก้ไขบทความ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">แก้ไขข้อมูลบทความ: {{ Str::limit($article->title, 50) }}</h3>
                        </div>
                        <form action="{{ route('admin.articles.update', $article) }}" method="POST" enctype="multipart/form-data">
                            @csrf @method('PUT')
                            <div class="card-body">
                                @if($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="form-group">
                                    <label for="title">หัวข้อบทความ <span class="text-danger">*</span></label>
                                    <input type="text"
                                           name="title"
                                           id="title"
                                           class="form-control @error('title') is-invalid @enderror"
                                           value="{{ old('title', $article->title) }}"
                                           placeholder="เช่น ข่าวสารและกิจกรรมล่าสุด"
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="content">เนื้อหาบทความ <span class="text-danger">*</span></label>
                                    <textarea name="content"
                                              id="content"
                                              class="form-control @error('content') is-invalid @enderror"
                                              rows="8"
                                              placeholder="เขียนเนื้อหาบทความที่นี่..."
                                              required>{{ old('content', $article->content) }}</textarea>
                                    @error('content')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="published_at">วันที่เผยแพร่</label>
                                    <input type="date"
                                           name="published_at"
                                           id="published_at"
                                           class="form-control @error('published_at') is-invalid @enderror"
                                           value="{{ old('published_at', $article->published_at ? $article->published_at->format('Y-m-d') : '') }}">
                                    @error('published_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">
                                        หากไม่ระบุ จะใช้วันที่ปัจจุบัน
                                    </small>
                                </div>

                                <div class="form-group">
                                    <label for="image">รูปภาพประกอบ</label>
                                    @if($article->image)
                                        <div class="mb-2">
                                            <img src="{{ \App\Helpers\ImageHelper::getImageUrl($article->image) }}"
                                                 class="img-thumbnail"
                                                 style="width: 300px; height: 200px; object-fit: cover;"
                                                 alt="รูปภาพบทความ">
                                            <p class="text-muted mt-1">รูปภาพปัจจุบัน</p>
                                        </div>
                                    @endif
                                    <div class="custom-file">
                                        <input type="file"
                                               name="image"
                                               id="image"
                                               class="custom-file-input @error('image') is-invalid @enderror"
                                               accept="image/*">
                                        <label class="custom-file-label" for="image">เลือกรูปภาพใหม่...</label>
                                        @error('image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <small class="form-text text-muted">
                                        รองรับไฟล์: JPEG, JPG, PNG, GIF, WebP (ขนาดไม่เกิน 2MB) - เลือกเฉพาะเมื่อต้องการเปลี่ยนรูปภาพ
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>บันทึกการแก้ไข
                                </button>
                                <a href="{{ route('admin.articles.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>ย้อนกลับ
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">ข้อมูลบทความ</h3>
                        </div>
                        <div class="card-body">
                            <p><strong>สร้างเมื่อ:</strong> {{ $article->created_at->format('d/m/Y H:i') }}</p>
                            <p><strong>แก้ไขล่าสุด:</strong> {{ $article->updated_at->format('d/m/Y H:i') }}</p>
                            @if($article->published_at)
                                <p><strong>วันที่เผยแพร่:</strong> {{ $article->published_at->format('d/m/Y') }}</p>
                            @else
                                <p><strong>วันที่เผยแพร่:</strong> <span class="text-muted">ไม่ระบุ</span></p>
                            @endif
                            @if($article->image)
                                <p><strong>มีรูปภาพ:</strong> <span class="text-success">ใช่</span></p>
                            @else
                                <p><strong>มีรูปภาพ:</strong> <span class="text-muted">ไม่มี</span></p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Custom file input label update
document.querySelector('.custom-file-input').addEventListener('change', function(e) {
    var fileName = e.target.files[0].name;
    var nextSibling = e.target.nextElementSibling;
    nextSibling.innerText = fileName;
});
</script>
@endsection