
<?php $__env->startSection('content'); ?>
    <h1>เนื้อหาหน้าแรก (จัดการหลังบ้าน)</h1>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Section</th>
                <th>หัวข้อ</th>
                <th>เนื้อหา</th>
                <th>รูปภาพ</th>
                <th>ปุ่ม</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $homepage_contents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($content->section); ?></td>
                    <td><?php echo e($content->title); ?></td>
                    <td><?php echo e(Str::limit($content->content, 50)); ?></td>
                    <td>
                        <img src="<?php echo e(\App\Helpers\ImageHelper::getImageUrl($content->image)); ?>"
                             style="max-width:80px;"
                             alt="รูปภาพหน้าแรก"
                             class="img-thumbnail">
                    </td>
                    <td>
                        <?php if($content->button_text): ?>
                            <span class="badge bg-primary"><?php echo e($content->button_text); ?></span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <a href="<?php echo e(route('admin.homepage.edit', $content)); ?>" class="btn btn-sm btn-warning">แก้ไข</a>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/homepage/index.blade.php ENDPATH**/ ?>