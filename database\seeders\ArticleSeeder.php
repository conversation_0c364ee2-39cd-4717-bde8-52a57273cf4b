<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Article;
use Illuminate\Support\Carbon;

class ArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Article::create([
            'title' => 'ขั้นตอนการจัดงานศพแบบครบวงจร',
            'content' => 'แนะนำขั้นตอนและสิ่งที่ควรเตรียมสำหรับการจัดงานศพ',
            'published_at' => Carbon::now(),
            'image' => null,
        ]);
        Article::create([
            'title' => 'เลือกแพ็กเกจงานศพอย่างไรให้เหมาะสม',
            'content' => 'เปรียบเทียบข้อดีข้อเสียของแต่ละแพ็กเกจ',
            'published_at' => Carbon::now()->subDays(3),
            'image' => null,
        ]);
    }
}
